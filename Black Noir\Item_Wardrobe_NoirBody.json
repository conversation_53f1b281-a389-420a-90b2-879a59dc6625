{
  "$type": "ThunderRoad.ItemData, ThunderRoad",
  "id": "Item.NoirBody",
  "sensitiveContent": "None",
  "sensitiveFilterBehaviour": "Discard",
  "version": 4,
  "localizationId": "NoirBody",
  "displayName": "Black Noir Suit",
  "description": "Black Noir's Suit.",
  "author": "Babyf",
  "valueType": "Gold",
  "value": 174.0,
  "rewardValue": 0.0,
  "tier": 2,
  "flags": "Spinnable",
  "levelRequired": 0,
  "category": "Exotics",
  "iconEffectId": null,
  "preferredItemCenter": "Mass",
  "drainImbueWhenIdle": true,
  "prefabAddress": "Item.NoirBody",
  "iconAddress": "Icon.NoirBody",
  "closeUpIconAddress": null,
  "pooledCount": 0,
  "androidPooledCount": 0,
  "type": "Wardrobe",
  "allowedStorage": "Inventory, Container, SandboxAllItems",
  "despawnOnStoredInInventory": false,
  "isStackable": false,
  "consumableId": null,
  "inventoryAudioContainerAddress": null,
  "inventoryAudioVolume_dB": 0.0,
  "slot": null,
  "snapAudioContainerAddress": "Bas.AudioGroup.Apparel.Leather",
  "snapAudioVolume_dB": 0.0,
  "overrideMassAndDrag": false,
  "mass": 0.7,
  "drag": 1.0,
  "angularDrag": 1.0,
  "focusRegenMultiplier": 0.91,
  "spellChargeSpeedPlayerMultiplier": 0.91,
  "spellChargeSpeedNPCMultiplier": 1.0,
  "collisionMaxOverride": 0,
  "collisionEnterOnly": false,
  "collisionNoMinVelocityCheck": false,
  "forceLayer": "None",
  "diffForceLayerWhenHeld": false,
  "forceLayerHeld": "None",
  "waterHandSpringMultiplierCurve": {
    "$type": "UnityEngine.AnimationCurve, UnityEngine.CoreModule",
    "keys": [
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 0.0,
        "value": 0.3,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      },
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 1.0,
        "value": 0.15,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      }
    ],
    "length": 2,
    "preWrapMode": "ClampForever",
    "postWrapMode": "ClampForever"
  },
  "waterDragMultiplierCurve": {
    "$type": "UnityEngine.AnimationCurve, UnityEngine.CoreModule",
    "keys": [
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 0.0,
        "value": 1.0,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      },
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 1.0,
        "value": 10.0,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      }
    ],
    "length": 2,
    "preWrapMode": "ClampForever",
    "postWrapMode": "ClampForever"
  },
  "waterSampleMinRadius": 0.2,
  "throwMultiplier": 1.0,
  "runSpeedMultiplier": 1.0,
  "flyRotationSpeed": 2.0,
  "flyThrowAngle": 0.0,
  "allowFlyBackwards": false,
  "telekinesisSafeDistance": 1.0,
  "telekinesisThrowRatio": 1.0,
  "telekinesisAutoGrabAnyHandle": false,
  "grippable": false,
  "grabAndGripClimb": false,
  "playerGrabAndGripChangeLayer": true,
  "customSnaps": [],
  "drainImbueOnSnap": true,
  "imbueEnergyOverTimeOnSnap": {
    "$type": "UnityEngine.AnimationCurve, UnityEngine.CoreModule",
    "keys": [
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 0.0,
        "value": 1.0,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      },
      {
        "$type": "UnityEngine.Keyframe, UnityEngine.CoreModule",
        "time": 3.0,
        "value": 0.0,
        "inTangent": 0.0,
        "outTangent": 0.0,
        "inWeight": 0.0,
        "outWeight": 0.0,
        "weightedMode": "None",
        "tangentMode": 0
      }
    ],
    "length": 2,
    "preWrapMode": "ClampForever",
    "postWrapMode": "ClampForever"
  },
  "modules": [
    {
      "$type": "ThunderRoad.ItemModuleWardrobe, ThunderRoad",
      "id": null,
      "sensitiveContent": "None",
      "sensitiveFilterBehaviour": "Discard",
      "version": 0,
      "category": "Apparel",
      "castShadows": "PlayerAndNPC",
      "wardrobes": [
        {
          "$type": "ThunderRoad.ItemModuleWardrobe+CreatureWardrobe, ThunderRoad",
          "creatureName": "HumanMale",
          "wardrobeDataAddress": "Data.NoirBody"
        },
      ],
      "isMetal": false,
      "groupPath": null
    },
    {
      "$type": "ThunderRoad.ItemModuleStats, ThunderRoad",
      "id": null,
      "sensitiveContent": "None",
      "sensitiveFilterBehaviour": "Discard",
      "version": 0,
      "stats": [
        {
          "$type": "ThunderRoad.ItemStatInt, ThunderRoad",
          "value": 2,
          "name": "Defense"
        },
        {
          "$type": "ThunderRoad.ItemStatInt, ThunderRoad",
          "value": 5,
          "name": "Mobility"
        },
        {
          "$type": "ThunderRoad.ItemStatInt, ThunderRoad",
          "value": 2,
          "name": "SpellCasting"
        },
        {
          "$type": "ThunderRoad.ItemStatInt, ThunderRoad",
          "value": 2,
          "name": "FocusRegen"
        }
      ],
      "groupPath": null
    }
  ],
  "colliderGroups": [],
  "damagers": [
    {
      "$type": "ThunderRoad.ItemData+Damager, ThunderRoad",
      "transformName": "Blunt",
      "damagerID": "PropBlunt"
    }
  ],
  "Interactables": [
    {
      "$type": "ThunderRoad.ItemData+Interactable, ThunderRoad",
      "transformName": "Handle",
      "interactableId": "ObjectHandleHeavy"
    },
    {
      "$type": "ThunderRoad.ItemData+Interactable, ThunderRoad",
      "transformName": "Handle2",
      "interactableId": "ObjectHandleHeavy"
    },
    {
      "$type": "ThunderRoad.ItemData+Interactable, ThunderRoad",
      "transformName": "Handle3",
      "interactableId": "ObjectHandleHeavy"
    },
    {
      "$type": "ThunderRoad.ItemData+Interactable, ThunderRoad",
      "transformName": "Handle4",
      "interactableId": "ObjectHandleHeavy"
    }
  ],
  "effectHinges": [],
  "whooshs": [
    {
      "$type": "ThunderRoad.ItemData+Whoosh, ThunderRoad",
      "transformName": "Whoosh",
      "effectId": "WhooshPropLight",
      "trigger": "Always",
      "stopOnSnap": true,
      "minVelocity": 5.0,
      "maxVelocity": 12.0,
      "dampening": 0.1
    }
  ],
  "entityModules": [],
  "groupPath": "Bandits/"
}