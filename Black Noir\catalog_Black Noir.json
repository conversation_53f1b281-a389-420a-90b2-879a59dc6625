{"m_LocatorId": "AddressablesMainContentCatalog", "m_InstanceProviderData": {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider"}, "m_Data": ""}, "m_SceneProviderData": {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.SceneProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.SceneProvider"}, "m_Data": ""}, "m_ResourceProviderData": [{"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider"}, "m_Data": ""}, {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"}, "m_Data": ""}, {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"}, "m_Data": ""}], "m_ProviderIds": ["UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider", "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"], "m_InternalIds": ["{ThunderRoad.FileManager.aaModPath}\\Black Noir\\blacknoir_assets_all.bundle", "{UnityEngine.AddressableAssets.Addressables.RuntimePath}\\default_assets_all.bundle", "{UnityEngine.AddressableAssets.Addressables.RuntimePath}\\fonts_assets_all.bundle", "Assets/Armor Mods/Black Noir/Blackbnboier/Armor.NoirBody.prefab", "Assets/Armor Mods/Black Noir/Blackbnboier/Armor.NoirHead.prefab", "Assets/Armor Mods/Black Noir/Blackbnboier/Data.NoirBody.asset", "Assets/Armor Mods/Black Noir/Blackbnboier/Data.NoirHead.asset", "Assets/Armor Mods/Black Noir/Blackbnboier/Icon.NoirBody.png", "Assets/Armor Mods/Black Noir/Blackbnboier/Icon.NoirHead.png", "Assets/Armor Mods/Black Noir/Blackbnboier/Item.NoirBody.prefab", "Assets/Armor Mods/Black Noir/Blackbnboier/Item.NoirHead.prefab", "Assets/SDK/Config/ThunderRoad/ThunderRoadSettings.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansCJKjp-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansCJKkr-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansCJKsc-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansCJKtc-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansMath-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular Outline SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular Overlay SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSansThai-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Sprite Assets/EmojiOne.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/TMP Settings.asset", "Assets/SDK/PostProcess/DefaultPostProcess.asset", "Assets/SDK/PostProcess/PostProcess_Cinematic.asset", "Assets/SDK/PostProcess/PostProcess_Filmic.asset", "Assets/SDK/PostProcess/PostProcess_HighContrast.asset", "Assets/SDK/PostProcess/PostProcess_Neutral.asset"], "m_KeyDataString": "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", "m_BucketDataString": "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", "m_EntryDataString": "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", "m_ExtraDataString": "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", "m_resourceTypes": [{"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.IAssetBundleResource"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.GameObject"}, {"m_AssemblyName": "ThunderRoad.Manikin, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.Manikin.ManikinGroupPart+PartLOD"}, {"m_AssemblyName": "ThunderRoad.Manikin, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.Manikin.AssetReferenceManikinPart"}, {"m_AssemblyName": "ThunderRoad.Manikin, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.Manikin.ManikinWardrobeData"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Texture2D"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Sprite"}, {"m_AssemblyName": "ThunderRoad, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.InteractableData"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Events.UnityEvent`1[[System.Single, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089]]"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Events.PersistentCallGroup"}, {"m_AssemblyName": "ThunderRoad, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.ThunderRoadSettings"}, {"m_AssemblyName": "UnityEngine.TextCoreFontEngineModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.TextCore.FaceInfo"}, {"m_AssemblyName": "UnityEngine.TextCoreFontEngineModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.TextCore.Glyph"}, {"m_AssemblyName": "UnityEngine.TextCoreFontEngineModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.TextCore.GlyphMetrics"}, {"m_AssemblyName": "UnityEngine.TextCoreFontEngineModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.TextCore.GlyphRect"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_Character"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.FaceInfo_Legacy"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.KerningTable"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_FontFeatureTable"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.FontAssetCreationSettings"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_FontWeightPair"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_FontAsset"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Material"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_SpriteCharacter"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_SpriteGlyph"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_Sprite"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_SpriteAsset"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_Settings"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.VolumeProfile"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.FloatParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.ClampedFloatParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.ColorParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.ColorAdjustments"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Vector2Parameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.BoolParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.Vignette"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.TonemappingModeParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.Tonemapping"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.MinFloatParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.ClampedIntParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.TextureParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.Bloom"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.MotionBlurModeParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.MotionBlurQualityParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.MotionBlur"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.TextureCurveParameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.TextureCurve"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.ColorCurves"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.SplitToning"}, {"m_AssemblyName": "Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Vector4Parameter"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.ShadowsMidtonesHighlights"}, {"m_AssemblyName": "Unity.RenderPipelines.Universal.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Rendering.Universal.WhiteBalance"}], "m_InternalIdPrefixes": []}