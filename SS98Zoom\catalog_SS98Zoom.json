{"m_LocatorId": "AddressablesMainContentCatalog", "m_BuildResultHash": "96c4ceddc01967216af975ac59ec97a1", "m_InstanceProviderData": {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider"}, "m_Data": ""}, "m_SceneProviderData": {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.SceneProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.SceneProvider"}, "m_Data": ""}, "m_ResourceProviderData": [{"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider"}, "m_Data": ""}, {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"}, "m_Data": ""}, {"m_Id": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider", "m_ObjectType": {"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"}, "m_Data": ""}], "m_ProviderIds": ["UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider", "UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider"], "m_InternalIds": ["{ThunderRoad.FileManager.aaDefaultPath}\\shaders_assets_all.bundle", "{ThunderRoad.FileManager.aaModPath}\\SS98Zoom\\ss98zoom_assets_all.bundle", "{ThunderRoad.FileManager.aaModPath}\\SS98Zoom\\SS98Zoom_unitybuiltinshaders.bundle", "{UnityEngine.AddressableAssets.Addressables.RuntimePath}\\fonts_assets_all.bundle", "Assets/Comissions/Zoom/Items/SS98.Zoom.Mask.asset", "Assets/Comissions/Zoom/Items/SS98.Zoom.Suit.asset", "Assets/Comissions/Zoom/Items/zoom_logo_by_deathdarkex_dbj8opq-pre.png", "Assets/Comissions/Zoom/Items/ZoomMask.prefab", "Assets/Comissions/Zoom/Items/ZoomMaskItem.png", "Assets/Comissions/Zoom/Items/ZoomMaskItem.prefab", "Assets/Comissions/Zoom/Items/ZoomSuit.prefab", "Assets/Comissions/Zoom/Items/ZoomSuitItem.png", "Assets/Comissions/Zoom/Items/ZoomSuitItem.prefab", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Dalgarian LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Dalgarian.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Calligraffitti/Calligraffitti-Regular SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Calligraffitti/Calligraffitti-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Caveat/Caveat-VariableFont_wght SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Caveat/Caveat-VariableFont_wght SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Estonia/Estonia-Regular SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Estonia/Estonia-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/<PERSON>_<PERSON>hade/JimNightshade-Regular SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/<PERSON>_<PERSON>hade/JimNightshade-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Medieval Sharp/MedievalSharp-Regular SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Medieval Sharp/MedievalSharp-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Petemoss/Petemoss-Regular SDF LIT.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/Lore/Petemoss/Petemoss-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-JP-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-KR-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Math-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular Outline SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular Overlay SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-SC-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-TC-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/Fonts & Materials/NotoSans-Thai-Regular SDF.asset", "Assets/SDK/Plugins/TextMesh Pro/Adressables/TMP Settings.asset", "Assets/SDK/Plugins/TextMesh Pro/Resources/Sprite Assets/EmojiOne.asset", "Assets/SDK/Plugins/TextMesh Pro/Shaders/TMP_SDF SSD.shader", "Assets/SDK/Plugins/TextMesh Pro/Shaders/TMP_SDF-Mobile.shader", "Assets/SDK/Plugins/TextMesh Pro/Shaders/TMP_SDF-URP Lit.shadergraph", "Assets/SDK/Plugins/TextMesh Pro/Shaders/TMP_Sprite.shader", "Assets/SDK/Shaders/AlwaysVisibleTexture.shader", "Assets/SDK/Shaders/SkyboxProceduralCustom-ASshader.ASshader", "Assets/SDK/Shaders/SuperSampleUI.shader", "Assets/SDK/Shaders/vfx_hdr_additive_flowmap.shader", "Assets/SDK/Shadowood/Foliage/Foliage-ASshader.ASshader", "Assets/SDK/Shadowood/LitMoss/LitMoss-ASshader.ASshader", "Assets/SDK/Shadowood/Ocean/Shaders/Shoreline-ASshader.ASshader", "Assets/SDK/Shadowood/River/River-ASshader.ASshader", "Assets/SDK/Shadowood/SkySorcery/SkySorcery-ASshader.ASshader", "Packages/com.unity.render-pipelines.core/Runtime/FallbackShader.shader", "Packages/com.unity.render-pipelines.universal/Shaders/Particles/ParticlesLit.shader", "Packages/com.unity.render-pipelines.universal/Shaders/Particles/ParticlesSimpleLit.shader", "Packages/com.unity.render-pipelines.universal/Shaders/Particles/ParticlesUnlit.shader", "Packages/com.unity.render-pipelines.universal/Shaders/PostProcessing/UberPost.shader", "Packages/com.unity.render-pipelines.universal/Shaders/Unlit.shader", "Packages/com.unity.render-pipelines.universal/Shaders/Utils/FallbackError.shader"], "m_KeyDataString": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "m_BucketDataString": "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", "m_EntryDataString": "bAAAAAAAAAAAAAAA/////wAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAP////8AAAAANwMAAAEAAAAAAAAAAgAAAAAAAAD/////AAAAAIYGAAACAAAAAAAAAAMAAAAAAAAA/////wAAAADHCQAAAwAAAAAAAAAEAAAAAQAAAHIAAACI683q/////wQAAAABAAAABQAAAAEAAAByAAAAiOvN6v////8IAAAAAQAAAAYAAAABAAAAcg<PERSON>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", "m_ExtraDataString": "B0xVbml0eS5SZXNvdXJjZU1hbmFnZXIsIFZlcnNpb249MC4wLjAuMCwgQ3VsdHVyZT1uZXV0cmFsLCBQdWJsaWNLZXlUb2tlbj1udWxsSlVuaXR5RW5naW5lLlJlc291cmNlTWFuYWdlbWVudC5SZXNvdXJjZVByb3ZpZGVycy5Bc3NldEJ1bmRsZVJlcXVlc3RPcHRpb25zmgIAAHsAIgBtAF8ASABhAHMAaAAiADoAIgA3ADQANwBhADcAYQA3ADAAMgA3AGYAYQAyADEAYgA0ADYANgBiAGUAZQBhADYAZABlADMAYgA0AGUAZQAxADUAIgAsACIAbQBfAEMAcgBjACIAOgAwACwAIgBtAF8AVABpAG0AZQBvAHUAdAAiADoAMAAsACIAbQBfAEMAaAB1AG4AawBlAGQAVAByAGEAbgBzAGYAZQByACIAOgBmAGEAbABzAGUALAAiAG0AXwBSAGUAZABpAHIAZQBjAHQATABpAG0AaQB0ACIAOgAtADEALAAiAG0AXwBSAGUAdAByAHkAQwBvAHUAbgB0ACIAOgAwACwAIgBtAF8AQgB1AG4AZABsAGUATgBhAG0AZQAiADoAIgAyAGQAYwA1ADgAYwBhAGEANQA5AGYAYgAzADcAOABkADUAOABjAGMAMwA0AGMAYgBlADUANgBhAGMAYQAxAGQAIgAsACIAbQBfAEEAcwBzAGUAdABMAG8AYQBkAE0AbwBkAGUAIgA6ADAALAAiAG0AXwBCAHUAbgBkAGwAZQBTAGkAegBlACIAOgA0ADAAOAAwADQALAAiAG0AXwBVAHMAZQBDAHIAYwBGAG8AcgBDAGEAYwBoAGUAZABCAHUAbgBkAGwAZQBzACIAOgB0AHIAdQBlACwAIgBtAF8AVQBzAGUAVQBXAFIARgBvAHIATABvAGMAYQBsAEIAdQBuAGQAbABlAHMAIgA6AGYAYQBsAHMAZQAsACIAbQBfAEMAbABlAGEAcgBPAHQAaABlAHIAQwBhAGMAaABlAGQAVgBlAHIAcwBpAG8AbgBzAFcAaABlAG4ATABvAGEAZABlAGQAIgA6AGYAYQBsAHMAZQB9AAdMVW5pdHkuUmVzb3VyY2VNYW5hZ2VyLCBWZXJzaW9uPTAuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49bnVsbEpVbml0eUVuZ2luZS5SZXNvdXJjZU1hbmFnZW1lbnQuUmVzb3VyY2VQcm92aWRlcnMuQXNzZXRCdW5kbGVSZXF1ZXN0T3B0aW9uc7ICAAB7ACIAbQBfAEgAYQBzAGgAIgA6ACIAMwA3AGIAOAA0ADIAMgBlADQAMwAxAGUAYgA1ADQANABjADgAZAA4AGYAMgA3AGMAYgA5ADYAYQA3ADcAZgA3ACIALAAiAG0AXwBDAHIAYwAiADoAMwA2ADAAMgA2ADcAMAA2ADgAOQAsACIAbQBfAFQAaQBtAGUAbwB1AHQAIgA6ADAALAAiAG0AXwBDAGgAdQBuAGsAZQBkAFQAcgBhAG4AcwBmAGUAcgAiADoAZgBhAGwAcwBlACwAIgBtAF8AUgBlAGQAaQByAGUAYwB0AEwAaQBtAGkAdAAiADoALQAxACwAIgBtAF8AUgBlAHQAcgB5AEMAbwB1AG4AdAAiADoAMAAsACIAbQBfAEIAdQBuAGQAbABlAE4AYQBtAGUAIgA6ACIAMgA1ADMANgAxADMAYwBiAGEANABmAGQAZgBiAGIANQA1ADIAOAAwADkAMwBhADIAMwBmADgANQA0AGMAZABkACIALAAiAG0AXwBBAHMAcwBlAHQATABvAGEAZABNAG8AZABlACIAOgAwACwAIgBtAF8AQgB1AG4AZABsAGUAUwBpAHoAZQAiADoAMgAyADgAMwA0ADYANgAxACwAIgBtAF8AVQBzAGUAQwByAGMARgBvAHIAQwBhAGMAaABlAGQAQgB1AG4AZABsAGUAcwAiADoAdAByAHUAZQAsACIAbQBfAFUAcwBlAFUAVwBSAEYAbwByAEwAbwBjAGEAbABCAHUAbgBkAGwAZQBzACIAOgBmAGEAbABzAGUALAAiAG0AXwBDAGwAZQBhAHIATwB0AGgAZQByAEMAYQBjAGgAZQBkAFYAZQByAHMAaQBvAG4AcwBXAGgAZQBuAEwAbwBhAGQAZQBkACIAOgBmAGEAbABzAGUAfQAHTFVuaXR5LlJlc291cmNlTWFuYWdlciwgVmVyc2lvbj0wLjAuMC4wLCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPW51bGxKVW5pdHlFbmdpbmUuUmVzb3VyY2VNYW5hZ2VtZW50LlJlc291cmNlUHJvdmlkZXJzLkFzc2V0QnVuZGxlUmVxdWVzdE9wdGlvbnOkAgAAewAiAG0AXwBIAGEAcwBoACIAOgAiAGMANAAzADgAMwBiAGYAYQAyAGQANABmADUAYQBiAGUAMwA4ADAAZAAzADEAZgA0AGMAZAA4ADUAYwBlADQAMAAiACwAIgBtAF8AQwByAGMAIgA6ADMANAA1ADMAMQAwADUAMwA3ADcALAAiAG0AXwBUAGkAbQBlAG8AdQB0ACIAOgAwACwAIgBtAF8AQwBoAHUAbgBrAGUAZABUAHIAYQBuAHMAZgBlAHIAIgA6AGYAYQBsAHMAZQAsACIAbQBfAFIAZQBkAGkAcgBlAGMAdABMAGkAbQBpAHQAIgA6AC0AMQAsACIAbQBfAFIAZQB0AHIAeQBDAG8AdQBuAHQAIgA6ADAALAAiAG0AXwBCAHUAbgBkAGwAZQBOAGEAbQBlACIAOgAiAFMAUwA5ADgAWgBvAG8AbQBfAHUAbgBpAHQAeQBiAHUAaQBsAHQAaQBuAHMAaABhAGQAZQByAHMAIgAsACIAbQBfAEEAcwBzAGUAdABMAG8AYQBkAE0AbwBkAGUAIgA6ADAALAAiAG0AXwBCAHUAbgBkAGwAZQBTAGkAegBlACIAOgAzADgANAA5ADYALAAiAG0AXwBVAHMAZQBDAHIAYwBGAG8AcgBDAGEAYwBoAGUAZABCAHUAbgBkAGwAZQBzACIAOgB0AHIAdQBlACwAIgBtAF8AVQBzAGUAVQBXAFIARgBvAHIATABvAGMAYQBsAEIAdQBuAGQAbABlAHMAIgA6AGYAYQBsAHMAZQAsACIAbQBfAEMAbABlAGEAcgBPAHQAaABlAHIAQwBhAGMAaABlAGQAVgBlAHIAcwBpAG8AbgBzAFcAaABlAG4ATABvAGEAZABlAGQAIgA6AGYAYQBsAHMAZQB9AAdMVW5pdHkuUmVzb3VyY2VNYW5hZ2VyLCBWZXJzaW9uPTAuMC4wLjAsIEN1bHR1cmU9bmV1dHJhbCwgUHVibGljS2V5VG9rZW49bnVsbEpVbml0eUVuZ2luZS5SZXNvdXJjZU1hbmFnZW1lbnQuUmVzb3VyY2VQcm92aWRlcnMuQXNzZXRCdW5kbGVSZXF1ZXN0T3B0aW9uc6ACAAB7ACIAbQBfAEgAYQBzAGgAIgA6ACIANABlADEAYQA1ADIAMwAxAGEAYgA4ADgAYQAzAGIAZAA2ADgANAA0ADMANAA1AGIAZAAzAGMAZgA5ADUAOQA4ACIALAAiAG0AXwBDAHIAYwAiADoAMAAsACIAbQBfAFQAaQBtAGUAbwB1AHQAIgA6ADAALAAiAG0AXwBDAGgAdQBuAGsAZQBkAFQAcgBhAG4AcwBmAGUAcgAiADoAZgBhAGwAcwBlACwAIgBtAF8AUgBlAGQAaQByAGUAYwB0AEwAaQBtAGkAdAAiADoALQAxACwAIgBtAF8AUgBlAHQAcgB5AEMAbwB1AG4AdAAiADoAMAAsACIAbQBfAEIAdQBuAGQAbABlAE4AYQBtAGUAIgA6ACIAMwA3AGUAYQAyAGIAZABkADIAMgBjAGEANABlADgANwAxAGUAYQAzADAAZAAxAGUAMQAyADAAMgA0ADAAOAA3ACIALAAiAG0AXwBBAHMAcwBlAHQATABvAGEAZABNAG8AZABlACIAOgAwACwAIgBtAF8AQgB1AG4AZABsAGUAUwBpAHoAZQAiADoAMQA1ADIAOQAzADMANAAwACwAIgBtAF8AVQBzAGUAQwByAGMARgBvAHIAQwBhAGMAaABlAGQAQgB1AG4AZABsAGUAcwAiADoAdAByAHUAZQAsACIAbQBfAFUAcwBlAFUAVwBSAEYAbwByAEwAbwBjAGEAbABCAHUAbgBkAGwAZQBzACIAOgBmAGEAbABzAGUALAAiAG0AXwBDAGwAZQBhAHIATwB0AGgAZQByAEMAYQBjAGgAZQBkAFYAZQByAHMAaQBvAG4AcwBXAGgAZQBuAEwAbwBhAGQAZQBkACIAOgBmAGEAbABzAGUAfQA=", "m_resourceTypes": [{"m_AssemblyName": "Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.ResourceManagement.ResourceProviders.IAssetBundleResource"}, {"m_AssemblyName": "ThunderRoad.Manikin, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "ThunderRoad.Manikin.ManikinWardrobeData"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Texture2D"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Sprite"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.GameObject"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_FontAsset"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Material"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_Settings"}, {"m_AssemblyName": "Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "TMPro.TMP_SpriteAsset"}, {"m_AssemblyName": "UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.Shader"}], "m_InternalIdPrefixes": []}